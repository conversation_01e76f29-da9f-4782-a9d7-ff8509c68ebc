# 编译配置命令

**配置编译目标为 ESP32S3：**

```bash
idf.py set-target esp32s3
```

**打开 menuconfig：**

```bash
idf.py menuconfig
```

**选择板子：**

```
<PERSON><PERSON> Assistant -> Board Type -> LILYGO T-Display-S3-Pro-MVSRLora
Or
<PERSON><PERSON> Assistant -> Board Type -> LILYGO T-Display-S3-Pro-MVSRLora_NO_BATTERY
```


**编译：**

```bash
idf.py build
```

<a href="https://github.com/Xinyuan-LilyGO/T-Display-S3-Pro" target="_blank" title="LILYGO T-Display-S3-Pro">LILYGO T-Display-S3-Pro</a>
<br />
<a href="https://github.com/Xinyuan-LilyGO/T-Display-S3-Pro-MVSRLora" target="_blank" title="LILYGO T-Display-S3-Pro-MVSRLora">LILYGO T-Display-S3-Pro-MVSRLora</a>