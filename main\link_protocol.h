#ifndef LINK_PROTOCOL_H
#define LINK_PROTOCOL_H
#include <stdio.h>
#include <vector>
#include <string>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"
#include <cJSON.h>
#include <string>
//LINK PROTOCOL CONSTANTS
#define LINK_PROTOCOL_MANUFACTURER_ID 0xFFFF

#ifdef __cplusplus
extern "C" {
#endif
// C-compatible Bluetooth scanning function declarations
void ble_app_scan(void);
void ble_app_on_sync(void);
void host_task(void *param);

// BLE5 Extended advertising function declaration
int adv_linkcid(const char *linkcid);

// BLE5 Extended scanning function declaration
int scan_linkcid(void);

// Device name setting function declaration
int ble_setname(const char *device_name);

#ifdef __cplusplus
}

// C++ function declarations (cannot be in extern "C" block)
std::string ble_scan_start_and_wait_json();
std::string ble_scan_linkcid_start_and_wait_json();

#endif

#endif // LINK_PROTOCOL_H
