#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_


#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE 24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_48
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_47

#define Module_4G_RX_PIN GPIO_NUM_21
#define Module_4G_TX_PIN GPIO_NUM_45

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_6
#define AUDIO_I2S_GPIO_WS GPIO_NUM_16
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_7  
#define AUDIO_I2S_GPIO_DIN GPIO_NUM_17
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_15

#define AUDIO_CODEC_I2C_SDA_PIN GPIO_NUM_4
#define AUDIO_CODEC_I2C_SCL_PIN GPIO_NUM_5
#define AUDIO_CODEC_ES8388_ADDR ES8388_CODEC_DEFAULT_ADDR

#define BOOT_BUTTON_GPIO GPIO_NUM_0

#define BUILTIN_LED_GPIO GPIO_NUM_1

#define LCD_SCLK_PIN GPIO_NUM_12
#define LCD_MOSI_PIN GPIO_NUM_11
#define LCD_MISO_PIN GPIO_NUM_13
#define LCD_DC_PIN GPIO_NUM_40
#define LCD_CS_PIN GPIO_NUM_39
#define LCD_RST_PIN GPIO_NUM_38

#define SPK_EN_PIN  GPIO_NUM_42
#define PHONE_CK_PIN GPIO_NUM_3

#define DISPLAY_WIDTH    160
#define DISPLAY_HEIGHT   80
#define DISPLAY_MIRROR_X false
#define DISPLAY_MIRROR_Y true
#define DISPLAY_SWAP_XY  true

#define DISPLAY_OFFSET_X 1
#define DISPLAY_OFFSET_Y 26

#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_41
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false

#endif // _BOARD_CONFIG_H_

