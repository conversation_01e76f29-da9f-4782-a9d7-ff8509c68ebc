#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_INPUT_REFERENCE    true

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_3
#define AUDIO_I2S_GPIO_WS GPIO_NUM_38
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_0
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_39
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_45

#define AUDIO_CODEC_PA_PIN       GPIO_NUM_NC
#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_47
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_48
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR  0x23

#define BUILTIN_LED_GPIO        GPIO_NUM_46
#define BOOT_BUTTON_GPIO        GPIO_NUM_0
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_NC

/* Expander */
#define DRV_IO_EXP_INPUT_MASK  (IO_EXPANDER_PIN_NUM_2 | IO_EXPANDER_PIN_NUM_12)


#define DISPLAY_WIDTH   240
#define DISPLAY_HEIGHT  320
#define DISPLAY_MIRROR_X false
#define DISPLAY_MIRROR_Y true
#define DISPLAY_SWAP_XY false

#define DISPLAY_OFFSET_X  0
#define DISPLAY_OFFSET_Y  0

#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_NC
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false

/* DFRobot K10 Camera pins */
#define PWDN_GPIO_NUM       -1
#define RESET_GPIO_NUM      -1
#define XCLK_GPIO_NUM       7

#define VSYNC_GPIO_NUM      4
#define HREF_GPIO_NUM       5
#define PCLK_GPIO_NUM       17
#define SIOD_GPIO_NUM       20
#define SIOC_GPIO_NUM       19

/* Camera pins */
#define CAMERA_PIN_PWDN     PWDN_GPIO_NUM
#define CAMERA_PIN_RESET    RESET_GPIO_NUM
#define CAMERA_PIN_XCLK     XCLK_GPIO_NUM
#define CAMERA_PIN_SIOD     SIOD_GPIO_NUM
#define CAMERA_PIN_SIOC     SIOC_GPIO_NUM

#define CAMERA_PIN_D9       6
#define CAMERA_PIN_D8       15
#define CAMERA_PIN_D7       16
#define CAMERA_PIN_D6       18
#define CAMERA_PIN_D5       9
#define CAMERA_PIN_D4       11
#define CAMERA_PIN_D3       10
#define CAMERA_PIN_D2       8
#define CAMERA_PIN_VSYNC    VSYNC_GPIO_NUM
#define CAMERA_PIN_HREF     HREF_GPIO_NUM
#define CAMERA_PIN_PCLK     PCLK_GPIO_NUM

#define XCLK_FREQ_HZ 20000000

#endif // _BOARD_CONFIG_H_
