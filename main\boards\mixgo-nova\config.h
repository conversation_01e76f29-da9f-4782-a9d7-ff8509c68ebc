#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_I2S_GPIO_MCLK     GPIO_NUM_35
#define AUDIO_I2S_GPIO_WS       GPIO_NUM_47
#define AUDIO_I2S_GPIO_BCLK     GPIO_NUM_34
#define AUDIO_I2S_GPIO_DIN      GPIO_NUM_33
#define AUDIO_I2S_GPIO_DOUT     GPIO_NUM_48

#define AUDIO_CODEC_PA_PIN      GPIO_NUM_NC
#define AUDIO_CODEC_I2C_SDA_PIN GPIO_NUM_37
#define AUDIO_CODEC_I2C_SCL_PIN GPIO_NUM_36
#define AUDIO_CODEC_ES8374_ADDR ES8374_CODEC_DEFAULT_ADDR

#define BUILTIN_LED_GPIO        GPIO_NUM_38
#define BOOT_BUTTON_GPIO        GPIO_NUM_0
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_NC

#define DISPLAY_MOSI_PIN        GPIO_NUM_40
#define DISPLAY_CLK_PIN         GPIO_NUM_41
#define DISPLAY_DC_PIN          GPIO_NUM_18
#define DISPLAY_CS_PIN          GPIO_NUM_45
#define DISPLAY_RST_PIN         GPIO_NUM_NC
#define DISPLAY_BACKLIGHT_PIN   GPIO_NUM_14
#define DISPLAY_WIDTH           128
#define DISPLAY_HEIGHT          160
#define DISPLAY_MIRROR_X        true
#define DISPLAY_MIRROR_Y        true
#define DISPLAY_SWAP_XY         false
#define DISPLAY_INVERT_COLOR    false
#define DISPLAY_OFFSET_X        2
#define DISPLAY_OFFSET_Y        1
#define DISPLAY_RGB_ORDER       LCD_RGB_ELEMENT_ORDER_RGB
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false

#endif // _BOARD_CONFIG_H_
